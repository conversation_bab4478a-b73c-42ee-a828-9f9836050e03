#!/usr/bin/env python3

import yfinance as yf
import pandas as pd
from datetime import datetime

def test_stock_data(symbol):
    print(f"\n=== Testing {symbol} ===")
    
    try:
        # Test ticker info
        ticker = yf.Ticker(symbol)
        info = ticker.info
        print(f"Ticker info available: {bool(info)}")
        
        if info:
            print(f"Company: {info.get('longName', 'N/A')}")
            print(f"Sector: {info.get('sector', 'N/A')}")
            print(f"Market Cap: {info.get('marketCap', 'N/A')}")
        
        # Test data download
        end = datetime.now()
        start = datetime(end.year - 1, end.month, end.day)
        
        print(f"Downloading data from {start.date()} to {end.date()}")
        df = yf.download(symbol, start=start, end=end, progress=False)
        
        if not df.empty:
            print(f"✅ Success! Downloaded {len(df)} rows")
            print(f"Columns: {df.columns.tolist()}")
            print(f"Date range: {df.index.min().date()} to {df.index.max().date()}")
            print(f"Sample data:")
            print(df.head(3))
            return True
        else:
            print("❌ No data returned")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    # Test various stock symbols
    symbols = [
        "INFY.NS",
        "RELIANCE.NS", 
        "TCS.NS",
        "HDFCBANK.NS",
        "INFY",  # Without .NS
        "^NSEI"  # Nifty index
    ]
    
    for symbol in symbols:
        test_stock_data(symbol)
        print("-" * 50)
