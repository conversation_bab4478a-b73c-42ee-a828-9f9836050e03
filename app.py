# **************** IMPORT PACKAGES ********************
from flask import Flask, render_template, request, jsonify
import pandas as pd
import numpy as np
from datetime import datetime
import os
import yfinance as yf
import warnings
import requests
from dotenv import load_dotenv  # Import dotenv for environment variables
# importing modules from models.py
from models import run_arima, run_lstm, run_lr

warnings.filterwarnings("ignore")

# ***************** FLASK *****************************
app = Flask(__name__)

# Load environment variables
load_dotenv()  # Take environment variables from .env file

NEWS_API_KEY = os.getenv("NEWS_API_KEY", "815d56be9aa94592ad061cce7dd571e1")  # Use env variable with fallback
NEWS_API_URL = 'https://newsapi.org/v2/everything'
ALPHA_VANTAGE_API_KEY = os.getenv("ALPHA_VANTAGE_API_KEY", "GVTB99LP1GHVJCVG")

# ****************** ROUTES ****************************
@app.route('/')
def home():
    return render_template('home.html')

@app.route('/index.html')
def stock_prediction():
    return render_template('index.html')

@app.route('/currency.html')
def currency_converter():
    return render_template('currency.html')

@app.route('/news.html')
def news():
    return render_template('news.html')

@app.route('/about.html')
def about():
    return render_template('about.html')

@app.route('/service.html')
def services():
    return render_template('service.html')

@app.route('/blog')
def blogs():
    return render_template('blog.html')

@app.route('/contact')
def contactus():
    return render_template('contact.html')

@app.route('/feature')
def features():
    return render_template('feature.html')

@app.route('/team')
def team():
    return render_template('team.html')

@app.route('/test_data/<symbol>')
def test_data(symbol):
    """Test route to debug data fetching"""
    try:
        if not symbol.endswith('.NS'):
            symbol += '.NS'

        # Test yfinance directly
        import yfinance as yf
        from datetime import datetime

        end = datetime.now()
        start = datetime(end.year - 1, end.month, end.day)  # Try 1 year instead of 2

        print(f"Testing yfinance for {symbol} from {start} to {end}")

        # Test basic yfinance call
        ticker = yf.Ticker(symbol)
        info = ticker.info

        result = f"<h2>Testing {symbol}</h2>"
        result += f"<p><strong>Ticker Info Available:</strong> {bool(info)}</p>"

        if info:
            result += f"<p><strong>Company Name:</strong> {info.get('longName', 'N/A')}</p>"
            result += f"<p><strong>Sector:</strong> {info.get('sector', 'N/A')}</p>"

        # Test data download
        df = yf.download(symbol, start=start, end=end, progress=False)
        result += f"<p><strong>Downloaded Data Shape:</strong> {df.shape}</p>"
        result += f"<p><strong>Downloaded Data Columns:</strong> {df.columns.tolist()}</p>"

        if not df.empty:
            result += f"<p><strong>Date Range:</strong> {df.index.min()} to {df.index.max()}</p>"
            result += f"<p><strong>Sample Data:</strong></p>"
            result += f"<pre>{df.head().to_string()}</pre>"

            # Test our function
            df_processed = get_historical(symbol)
            if df_processed is not None:
                result += f"<p><strong>Our Function Result:</strong> Success! {len(df_processed)} rows</p>"
            else:
                result += f"<p><strong>Our Function Result:</strong> Failed</p>"
        else:
            result += f"<p><strong>Error:</strong> No data returned from yfinance</p>"

        return result

    except Exception as e:
        import traceback
        return f"<h2>Error testing {symbol}</h2><pre>{traceback.format_exc()}</pre>"

# ***************** HELPER FUNCTIONS ***************************
def get_historical(quote):
    try:
        print(f"Fetching historical data for {quote}...")
        end = datetime.now()
        start = datetime(end.year - 2, end.month, end.day)

        # Try yfinance first
        df = yf.download(quote, start=start, end=end, progress=False)

        if not df.empty:
            print(f"Raw data shape: {df.shape}")
            print(f"Raw data columns: {df.columns.tolist()}")

            # Handle multi-level columns from yfinance
            if isinstance(df.columns, pd.MultiIndex):
                print("Detected multi-level columns, flattening...")
                # Flatten multi-level columns by taking the first level (the actual column names)
                df.columns = [col[0] if isinstance(col, tuple) else col for col in df.columns]
                print(f"Flattened columns: {df.columns.tolist()}")

            # Reset index to get Date as a column
            df = df.reset_index()
            print(f"After reset_index - columns: {df.columns.tolist()}")

            # Handle different possible date column names
            date_column = None
            for col in ['Date', 'Datetime', 'date', 'datetime']:
                if col in df.columns:
                    date_column = col
                    break

            if date_column:
                df['Date'] = pd.to_datetime(df[date_column])
                if date_column != 'Date':
                    df = df.drop(columns=[date_column])
                df.set_index('Date', inplace=True)
            else:
                # If no date column found, use the index
                if df.index.name in ['Date', 'Datetime'] or pd.api.types.is_datetime64_any_dtype(df.index):
                    df.index.name = 'Date'
                else:
                    raise ValueError("No valid date column found in the downloaded data.")

            # Ensure we have the required columns
            required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            available_columns = df.columns.tolist()
            print(f"Available columns: {available_columns}")

            # Check for missing columns and handle them
            missing_columns = [col for col in required_columns if col not in available_columns]
            if missing_columns:
                print(f"Missing columns: {missing_columns}")
                # Try to create missing columns with default values if possible
                for col in missing_columns:
                    if col == 'Volume' and col not in df.columns:
                        df['Volume'] = 0  # Default volume
                        print(f"Added default Volume column")

            # Add Adj Close if not present (use Close as fallback)
            if 'Adj Close' not in df.columns and 'Close' in df.columns:
                df['Adj Close'] = df['Close']
                print("Added Adj Close column using Close values")

            # Final check for critical columns
            critical_columns = ['Open', 'High', 'Low', 'Close']
            final_missing = [col for col in critical_columns if col not in df.columns]
            if final_missing:
                raise ValueError(f"Missing critical columns: {final_missing}")

            # Remove any rows with NaN values in critical columns
            df = df.dropna(subset=critical_columns)

            if len(df) == 0:
                raise ValueError("No valid data rows after cleaning")

            # Ensure all price columns are numeric
            for col in ['Open', 'High', 'Low', 'Close', 'Adj Close']:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            # Ensure Volume is numeric
            if 'Volume' in df.columns:
                df['Volume'] = pd.to_numeric(df['Volume'], errors='coerce').fillna(0)

            df.to_csv(f'{quote}.csv')
            print(f"Successfully fetched {len(df)} days of data for {quote}")
            print(f"Date range: {df.index.min()} to {df.index.max()}")
            print(f"Final columns: {df.columns.tolist()}")
            return df
        else:
            # Fallback to Alpha Vantage
            print(f"yfinance returned empty data, trying Alpha Vantage for {quote}...")
            try:
                from alpha_vantage.timeseries import TimeSeries
                ts = TimeSeries(key=ALPHA_VANTAGE_API_KEY, output_format='pandas')
                data, _ = ts.get_daily_adjusted(symbol=f'NSE:{quote}', outputsize='full')
                data = data.head(503).iloc[::-1].reset_index()
                df = data[['date', '1. open', '2. high', '3. low', '4. close', '5. adjusted close', '6. volume']]
                df.columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Adj Close', 'Volume']
                df['Date'] = pd.to_datetime(df['Date'])
                df.set_index('Date', inplace=True)
                df.to_csv(f'{quote}.csv')
                print(f"Successfully fetched {len(df)} days of data from Alpha Vantage for {quote}")
                return df
            except Exception as av_error:
                print(f"Alpha Vantage also failed: {av_error}")
                raise ValueError(f"Both yfinance and Alpha Vantage failed to fetch data for {quote}")

    except Exception as e:
        print(f"Error fetching historical data for {quote}: {e}")
        import traceback
        traceback.print_exc()
        return None

@app.route('/get_news', methods=['POST'])
def get_news():
    symbol = request.form.get('symbol')
    
    if not symbol:
        return jsonify({'error': 'No stock symbol provided'}), 400

    query = f"{symbol} business"
    
    try:
        response = requests.get(NEWS_API_URL, params={
            'q': query,
            'apiKey': NEWS_API_KEY,
            'sortBy': 'publishedAt'
        })
        
        if response.status_code != 200:
            return jsonify({'error': 'Failed to fetch news'}), response.status_code
        
        data = response.json()

        if data['status'] == 'ok':
            return jsonify(data['articles'])
        else:
            return jsonify({'error': 'Error fetching news from the API'}), 500

    except requests.exceptions.RequestException as e:
        return jsonify({'error': str(e)}), 500

@app.route('/stock_details', methods=['POST'])
def stock_details():
    if request.method == 'POST':
        stock_symbol = request.form['stock_symbol'].upper()
        if not stock_symbol.endswith('.NS'):
            stock_symbol += '.NS'
        stock = yf.Ticker(stock_symbol)
        stock_info = stock.info or {}

        # Fetching additional company details
        stock_name = stock_info.get('longName', 'N/A')
        sector = stock_info.get('sector', 'N/A')
        industry = stock_info.get('industry', 'N/A')
        description = stock_info.get('longBusinessSummary', 'N/A')

        # Fetching stock splits
        stock_splits = stock.splits

        # Fetching dividends
        dividends = stock.dividends

        # Fetching financial statements
        income_statement = stock.financials
        balance_sheet = stock.balance_sheet
        cash_flow = stock.cashflow
        market_cap = stock_info.get('marketCap', 'N/A')
        pe_ratio = stock_info.get('trailingPE', 'N/A')
        pb_ratio = stock_info.get('priceToBook', 'N/A')
        dividend_yield = stock_info.get('dividendYield', 'N/A')
        high_price = stock_info.get('dayHigh', 'N/A')
        low_price = stock_info.get('dayLow', 'N/A')
        roe = stock_info.get('returnOnEquity', 'N/A')
        fifty_two_week_high = stock_info.get('fiftyTwoWeekHigh', 'N/A')
        fifty_two_week_low = stock_info.get('fiftyTwoWeekLow', 'N/A')
        no_of_shares = stock_info.get('sharesOutstanding', 'N/A')
        enterprise_value = stock_info.get('enterpriseValue', 'N/A')
        debt = stock_info.get('totalDebt', 'N/A')

        try:
            df = get_historical(stock_symbol)
            if df is None or df.empty:
                raise ValueError(f"Failed to retrieve data for {stock_symbol}. Please check if the stock symbol is correct.")

            df["Code"] = stock_symbol

            # Check if we have enough data
            if len(df) < 14:
                raise ValueError(f"Insufficient data for {stock_symbol}. Need at least 14 days of data for predictions.")

            latest_data = df.iloc[-1]
            todays_open = latest_data.get('Open', 'N/A')
            todays_close = latest_data.get('Close', 'N/A')
            todays_adj_close = latest_data.get('Adj Close', 'N/A')
            todays_volume = latest_data.get('Volume', 'N/A')
            todays_high = latest_data.get('High', 'N/A')
            todays_low = latest_data.get('Low', 'N/A')

            # Run prediction models with error handling
            print(f"Running predictions for {stock_symbol}...")
            ARIMA_pred, error_ARIMA = run_arima(df)
            LSTM_pred, error_LSTM = run_lstm(df)
            LR_pred, error_LR = run_lr(df)
            print(f"Predictions completed for {stock_symbol}")

            return render_template(
                'stock_details.html',
                stock_symbol=stock_symbol,
                stock_name=stock_name,
                sector=sector,
                industry=industry,
                description=description,
                stock_splits=stock_splits.to_dict() if not stock_splits.empty else 'N/A',
                dividends=dividends.to_dict() if not dividends.empty else 'N/A',
                income_statement=income_statement.to_dict() if not income_statement.empty else 'N/A',
                balance_sheet=balance_sheet.to_dict() if not balance_sheet.empty else 'N/A',
                cash_flow=cash_flow.to_dict() if not cash_flow.empty else 'N/A',
                market_cap=market_cap,
                pe_ratio=pe_ratio,
                pb_ratio=pb_ratio,
                dividend_yield=dividend_yield,
                roe=roe,
                fifty_two_week_high=fifty_two_week_high,
                fifty_two_week_low=fifty_two_week_low,
                no_of_shares=no_of_shares,
                enterprise_value=enterprise_value,
                debt=debt,
                todays_open=todays_open,
                todays_close=todays_close,
                todays_adj_close=todays_adj_close,
                todays_volume=todays_volume,
                todays_high=todays_high,
                todays_low=todays_low,
                high_price=high_price,
                low_price=low_price,
                ARIMA_pred=ARIMA_pred,
                LSTM_pred=LSTM_pred,
                LR_pred=LR_pred,
                error_ARIMA=error_ARIMA,
                error_LSTM=error_LSTM,
                error_LR=error_LR)
        except Exception as e:
            return render_template('error.html', error_message=str(e))

# ******************* MAIN *******************************
if __name__ == "__main__":
    app.run(debug=True)
