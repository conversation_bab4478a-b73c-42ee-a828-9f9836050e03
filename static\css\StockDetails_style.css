/* General styles */
html {
    scroll-behavior: smooth;
    overflow-x: hidden;
    /* Fix horizontal scrolling */
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: #f4f7fc;
    margin: 0;
    padding: 0;
    color: #333;
}

/* Container styling */
.container {
    width: 100%;
    padding: 0px;
    box-sizing: border-box;
    max-width: 1920px;
    margin: 0px;
}

/* Header styling */
.pageheader {
    background: linear-gradient(135deg, #1e3c72, #2a5298);
    color: #fff;
    text-align: center;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
}

.logo {
    width: 50px;
    height: 50px;
    margin-right: 10px;
    border-radius: 50%;
    object-fit: cover;
}

.pageheader h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: 600;
    display: inline-block;
}

/* Adjust header text size for smaller screens */
@media screen and (max-width: 768px) {
    .pageheader h1 {
        font-size: 1.5rem;
    }

    .logo {
        width: 40px;
        height: 40px;
    }
}

/* Center section headings */
section h2 {
    text-align: center;
    font-size: 2rem;
    color: #333;
    margin-bottom: 20px;
}

/* Search bar styling */
.search-bar {
    margin-top: 20px;
    display: flex;
    justify-content: center;
}

.search-container {
    display: flex;
    justify-content: center;
}

.search-box {
    width: 100%;
    max-width: 300px;
    padding: 12px;
    border-radius: 50px;
    border: 1px solid #ddd;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    outline: none;
    font-size: 1rem;
}

.search-box:focus {
    border-color: #1e3c72;
}

.search-btn {
    background: #1e3c72;
    color: #fff;
    border: none;
    border-radius: 50px;
    padding: 12px 20px;
    margin-left: 10px;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.search-btn:hover {
    background-color: #2a5298;
}

/* Responsive search bar */
@media screen and (max-width: 768px) {
    .search-box {
        max-width: 250px;
        padding: 10px;
    }

    .search-btn {
        padding: 10px 15px;
    }
}

/* Stock details header */
.header {
    text-align: center;
    background-color: #1e3c72;
    color: #fff;
    padding: 20px;
    margin-top: 20px;
}

.header h1 {
    margin: 0;
    font-size: 2rem;
}

/* Price info section */
.price-info {
    margin-top: 10px;
}

.price-info .price {
    font-size: 2rem;
    font-weight: bold;
}

.exchange-info {
    display: block;
    font-size: 1rem;
    color: #ccc;
}

/* Navbar styling */
.navbar {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
    white-space: nowrap;
    overflow-x: auto;
    padding: 0 20px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar a {
    color: #333;
    padding: 14px 20px;
    text-decoration: none;
    font-size: 1rem;
    transition: background-color 0.3s ease;
    border-radius: 4px;
    margin: 0 10px;
    flex-shrink: 0;
}

.navbar a:hover {
    background-color: #ddd;
}

/* Stock data cards */
.stock-data-container {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    margin-top: 20px;
}

.stock-card {
    width: 150px;
    padding: 20px;
    text-align: center;
    border-radius: 10px;
    color: #fff;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    margin: 10px;
}

.stock-card:hover {
    transform: scale(1.05);
}

/* Specific colors for stock data cards */
.stock-card.blue {
    background-color: #1e3c72;
}

.stock-card.yellow {
    background-color: #ffc107;
}

.stock-card.green {
    background-color: #28a745;
}

.stock-card.red {
    background-color: #dc3545;
}

/* Adjust stock card size on smaller screens */
@media screen and (max-width: 768px) {
    .stock-card {
        width: 120px;
        padding: 15px;
    }
}

/* Chart Section */
.chart-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    max-width: 100%;
    margin: 0 3px;
    padding: 20px;
}

.chart {
    width: 100%;
   
    margin-bottom: 10px;
    text-align: center;
}

#chart .chart {
    /* width: 100%; */
    max-width: 100%;
    min-width: 95%;
    margin-bottom: 10px;
    text-align: center;
}

.chart-container .tradingview_ba3c5 {
    width: 100%;
}

/* Responsive chart size */
@media screen and (max-width: 768px) {
    .chart {
        height: auto;
    }
    .chart-container .tradingview_ba3c5 {
        width: 100%;
    }
}

/* Forecast Price Section */
.forecast-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  
    justify-items: center;
    align-content: center;
    justify-content: space-between;
}


@media screen and (max-width: 768px) {
    .forecast-grid {
        grid-template-columns: 1fr;
    }

    .forecast-grid .prediction-item img {
        width: 100%;

    }
}

.overview {
    padding: 30px;
    background-color: #f0f4f8;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-top: 40px;
}

.overview h2 {
    text-align: center;
    font-size: 28px;
    margin-bottom: 20px;
    color: #333;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.info-item {
    padding: 15px;
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
}

.info-item .label {
    font-weight: bold;
    color: #333;
}

.info-item .value {
    color: #555;
}

/* Custom styling for the description */
.info-item.description {
    grid-column: span 2; /* Make the description span across both columns */
    font-size: 18px;
    line-height: 1.6;
    padding: 20px;
    background-color: #eaf0f8;
    border-left: 4px solid #007acc; /* Accent border for description */
}

.info-item.description .value {
    color: #333;
    font-size: 18px;
    text-align: justify;
}

.label {
    font-weight: 600;
    color: #007bff;
}

.value {
    font-weight: 400;
    color: #333;
}

@media screen and (max-width: 600px) {
    .info-grid {
        grid-template-columns: 1fr;
    }
}


/* Prediction Details */
.prediction-details {
    margin-top: 30px;
}

.prediction-details h2 {
    text-align: center;
    font-size: 1.8rem;
    color: #333;
    margin-top: 40px;
}

.prediction-card {
    width: 200px;
    padding: 15px;
    text-align: center;
    border-radius: 10px;
    color: #fff;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    margin: 10px auto;
}

.prediction-card p {
    font-weight: bold;
    font-size: 1rem;
}

.prediction-card.green {
    background-color: #28a745;
}

.prediction-card.blue {
    background-color: #1e3c72;
}

.prediction-card.yellow {
    background-color: #ffc107;
}

.prediction-card.red {
    background-color: #dc3545;
}

.prediction-card:hover {
    transform: scale(1.05);
}
/* my updates */
.suggestions {
    margin-top: 30px;
    padding: 20px;
    background-color: #f4f7fa;
    border: 1px solid #dcdde1;
    border-radius: 10px;
    max-width: 80%;
    margin-left: auto;
    margin-right: auto;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.suggestions h2 {
    text-align: center;
    color: #2f3640;
    font-size: 24px;
    font-weight: 600;
}

.suggestion-card {
    margin: 20px 0;
    padding: 20px;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    background-color: #ffffff;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.suggestion-card:hover {
    transform: scale(1.02);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.suggestion-card h3 {
    margin: 0;
    font-size: 20px;
    color: #273c75;
}

.buy {
    color: #44bd32;
    font-weight: bold;
    font-size: 18px;
}

.sell {
    color: #e84118;
    font-weight: bold;
    font-size: 18px;
}

.hold {
    color: #fbc531;
    font-weight: bold;
    font-size: 18px;
}

.suggestion-card p {
    margin-top: 10px;
    font-size: 16px;
    color: #4b4b4b;
}
.pros-cons-section {
    margin-top: 40px;
    padding: 30px;
    background-color: #f0f4f8;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.pros-cons-section h2 {
    text-align: center;
    color: #333;
    font-size: 28px;
    margin-bottom: 20px;
}

.pros-cons-container {
    display: flex;
    justify-content: space-between;
    gap: 20px;
}

.pros-container, .cons-container {
    width: 48%;
    padding: 20px;
    border-radius: 8px;
    background-color: #fff;
    border: 1px solid #ccc;
    transition: box-shadow 0.3s ease;
}

.pros-container:hover, .cons-container:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.pros-container h3, .cons-container h3 {
    color: #2c3e50;
    font-size: 22px;
    margin-bottom: 15px;
}

.pros-list, .cons-list {
    list-style-type: none;
    padding-left: 0;
}

.pros-list li, .cons-list li {
    font-size: 16px;
    color: #555;
    margin-bottom: 10px;
    line-height: 1.6;
}

.pros-list li::before {
    content: '✔';
    color: green;
    margin-right: 10px;
}

.cons-list li::before {
    content: '✖';
    color: red;
    margin-right: 10px;
}
.table-section {
    width: 100%;
    overflow-x: auto;
    margin-top: 20px;
}

.table-section table {
    width: 100%;
    border-collapse: collapse;
}

.table-section table th, 
.table-section table td {
    padding: 10px;
    text-align: left;
    border: 1px solid #ddd;
    white-space: nowrap;
}

.table-section table th {
    background-color: #f9f9f9;
    font-weight: bold;
}

.table-section table tr:nth-child(even) {
    background-color: #f2f2f2;
}

.table-section table tr:hover {
    background-color: #f1f1f1;
}

.tabcontent {
    margin-top: 20px;
    display: none;
    padding: 10px;
    border: 1px solid #ccc;
    border-top: none;
}

.tablinks.active {
    background-color: #ccc;
}

/* mu updates */

/* Footer styling */
.footer {
    text-align: center;
    padding: 20px;
    background-color: rgba(0, 32, 74, 0.9);
    color: white;
    margin-top: 50px;
}

.footer p {
    margin: 0;
    font-size: 0.9rem;
}

.forecast-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-left: 100px ;
}
.img-cont{
    width: max-content;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.3); /* Shadow with blur and opacity */
    padding: 5px; /* Optional: Adds padding to the container */
    border-radius: 10px; /* Optional: Adds rounded corners to the container */
    background-color: white; /* Optional: Background for the image container */
}
.model-block {
    display: flex;
    flex-direction: column; /* Stacks content vertically */
    text-align: center;
    margin: 10px;
}
.model-prediction {
    display: flex;
    justify-content: space-around; /* Ensures space between models */
    align-items: flex-start; /* Aligns items to the top */
    margin-top: 20px;
}
